Of course. Here is a detailed, step-by-step prompt designed for an AI coding agent to complete Stage 1 of the project.

---

### **Prompt for AI Coding Agent: Stage 1**

**Project:** AI Video Clipper
**Current Stage:** Stage 1 - The Foundation: Project Setup & Static UI
**Goal:** Initialize a new Laravel project and build the static HTML and CSS for the three main user interface pages: Upload, Processing, and Download. No backend functionality is required yet.

**Technology Stack:**
*   Backend Framework: **Laravel**
*   Frontend Templating: **Blade**
*   CSS Framework: **Tailwind CSS**

Please execute the following steps precisely.

---

**Step 1: Initialize the Laravel Project**
Create a new Laravel project named `ai-clipper`. Use the following composer command:
`composer create-project laravel/laravel ai-clipper`

**Step 2: Set Up Tailwind CSS**
Navigate into the new project directory. I want to use Tailwind CSS for styling. Please follow the official installation guide for Laravel:
1.  Install the required npm packages (`tailwindcss`, `postcss`, `autoprefixer`).
2.  Generate the `tailwind.config.js` and `postcss.config.js` files using `npx tailwindcss init -p`.
3.  Configure the `content` path in `tailwind.config.js` to scan Blade files.
4.  Add the Tailwind directives (`@tailwind base;`, `@tailwind components;`, `@tailwind utilities;`) to the `resources/css/app.css` file.

**Step 3: Create a Master Layout File**
Create a single, reusable Blade layout file to serve as the template for all pages.
*   **File Location:** `resources/views/layouts/app.blade.php`
*   **Content:**
    *   Standard HTML5 boilerplate.
    *   In the `<head>`, include the compiled `app.css` using `Vite`.
    *   Set a dark background color for the `<body>` (e.g., `bg-gray-900 text-white`).
    *   The body should contain a main container that centers its content on the page.
    *   Use a `@yield('content')` directive inside the main container where the page-specific content will go.

**Step 4: Define Routes**
For now, we will create three simple routes to view our static pages. Modify the `routes/web.php` file:
1.  The root route `/` should return a view named `upload`.
2.  A route `/processing` should return a view named `processing`.
3.  A route `/results` should return a view named `results`.

**Step 5: Build the "Upload" Page**
Create the Blade file for the main upload interface.
*   **File Location:** `resources/views/upload.blade.php`
*   **Instructions:**
    *   This view should `@extend('layouts.app')` and define a `@section('content')`.
    *   **Title:** Add an `<h1>` element with the text "AI Video Clipper".
    *   **File Upload Area:** Create a large, dashed-border `div` that acts as a dropzone. Inside, include text like "Drag & Drop Your Video Here" and a styled "Choose File" button.
    *   **Configuration Section:** Below the dropzone, create a small form section with two inputs:
        1.  A `label` and a `select` dropdown for "Number of clips" with options for 3, 5, and 10.
        2.  A `label` and a `text` input for "Approximate clip duration (seconds)".
    *   **Submit Button:** Add a large, prominently styled primary button with the text "Generate Clips".

**Step 6: Build the "Processing" Page**
Create the Blade file for the waiting/processing screen.
*   **File Location:** `resources/views/processing.blade.php`
*   **Instructions:**
    *   This view should also extend the `layouts.app`.
    *   **Title:** Add an `<h2>` element: "Your video is being processed..."
    *   **Spinner:** Add a simple, spinning loading indicator using Tailwind CSS animations.
    *   **Status List:** Create a static, ordered list (`<ol>`) that shows the steps of the process. This is for UI design only.
        *   `<li>Analyzing audio and creating transcript...</li>`
        *   `<li>Asking AI to find the best moments...</li>`
        *   `<li>Clipping your video...</li>`
        *   `<li>Finalizing clips...</li>`
        *   Style the text of the current step to be more prominent than the others.

**Step 7: Build the "Results" Page**
Create the Blade file for displaying the final clips.
*   **File Location:** `resources/views/results.blade.php`
*   **Instructions:**
    *   Extend the `layouts.app` layout.
    *   **Title:** An `<h1>`: "Your Clips Are Ready!"
    *   **Results Grid:** Create a responsive grid (e.g., `grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4`).
    *   **Clip Card (Static Example):** Inside the grid, design a single "card" component as a template. This card should contain:
        *   A placeholder `div` with an aspect ratio of 16/9 and a gray background, representing the video preview.
        *   A title, e.g., "Clip 1 (0:52s)".
        *   A paragraph for the "AI Justification": "Reason: This clip contains a strong emotional peak and a clear call to action."
        *   A "Download" button.
    *   **Repeat the Card:** Duplicate this static card 2-3 times to visualize how the grid will look.
    *   **Start Over Button:** At the bottom of the page, add a "Process Another Video" button or link that would eventually point back to the homepage.

**Final Verification:**
Once you have completed all steps, run `npm run dev` in one terminal and `php artisan serve` in another. I should be able to visit `http://127.0.0.1:8000`, see the fully styled Upload page, and manually navigate to `/processing` and `/results` to see their static designs. All pages should have a consistent, modern dark theme.